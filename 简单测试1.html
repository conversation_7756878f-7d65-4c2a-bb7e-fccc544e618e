<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8" />
    <title>vue测试</title>
    <meta name="description" content="aipha" />

    <meta
      content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0"
      name="viewport"
    />
    <script src="./js/vue.min.js"></script>

    <style>
      .pc_menuPage {
        width: 360px;
        height: 740px;
        overflow: hidden;
        margin: auto auto;
        -webkit-user-drag: none;
        -webkit-user-select: none;
        position: relative;
        top: 50%;
        transform: translateY(10%);
        background-color: #f4f4f4;
      }
      #app {
        position: absolute;
        top: 0;
        bottom: 0;
        left: 0;
        right: 0;
        overflow: hidden;
        -webkit-overflow-scroll: touch;
        font-family: Arial, Helvetica, sans-serif;
        box-sizing: content-box;
      }
      .app_container {
        position: relative;
        height: 100%;
        width: 100%;
        display: flex;
        flex-direction: column;
      }
      .header {
        height: 50px;
        background-color: pink;
        display: flex;
        flex-wrap: nowrap;
        overflow-x: auto;
      }
      .content {
        display: flex;
        flex-direction: column;
        flex: 1;
        padding: 0;
        overflow: auto;
        background-color: #ccc;
      }
      .data {
        width: 100%;
        height: 100px;
        border: 1px solid #000;
        flex-shrink: 0;
      }
      .footer {
        width: 360px;
        height: 50px;
        background-color: green;
      }
      .test-time-simulation-container {
        position: absolute;
        top: 180px;
        right: 0px;
        z-index: 12;
        display: flex;
        height: 40px;
        min-width: 200px;
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15), 0 2px 4px rgba(0, 0, 0, 0.1);
        border-radius: 6px 0 0 6px;
        transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1),
          width 0.4s cubic-bezier(0.4, 0, 0.2, 1);

        transform: translateX(calc(100% - 40px));
      }
      .header-nav {
        display: flex;
        flex-direction: column;
        width: 50px;
        flex-shrink: 0;
        margin-right: 10px;
        background-color: #ccc;
      }
      .active {
        background-color: red;
      }
    </style>
  </head>

  <body class="pc pc_menuPage">
    <div id="app">
      <div class="test-time-simulation-container">565656</div>
      <div class="app_container">
        <div class="header">
          <div
            :data-index="item"
            class="header-nav"
            @click="handleNavScroll(item)"
            :class="{ active: isActive === item }"
            v-for="item in 50"
            :key="item"
          >
            {{item}}
          </div>
        </div>
        <div class="content" ref="contentContainer">
          <div class="data" v-for="item in 50" :key="item" ref="contentItems">{{item}}</div>
        </div>
        <div class="footer"></div>
      </div>
    </div>

    <script>
      const app = new Vue({
        el: "#app",
        components: {},
        data: {
          isActive: 1
        },
        created() {},
        mounted() {},
        // watch: {
        //   hh: {
        //     handler(val, odlVal) {
        //       this.hh = val.toUpperCase()
        //       console.log(this.hh, 'this.hh ')
        //     },
        //     deep: true
        //   }
        // },
        computed: {},
        methods: {
          handleNavScroll(item) {
            this.isActive = item

            // 滚动到对应的内容区item
            const contentItems = this.$refs.contentItems
            if (contentItems && contentItems[item - 1]) {
              contentItems[item - 1].scrollIntoView({
                behavior: "smooth",
                block: "start"
              })
            }
          }
        }
      })
    </script>
  </body>
</html>
